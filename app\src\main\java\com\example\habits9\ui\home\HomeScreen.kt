package com.example.habits9.ui.home

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.habits9.ui.components.SortMenuButton
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import com.example.habits9.ui.components.NumericalInputDialog

// Dark theme colors from style guide
val BackgroundDark = Color(0xFF121826)
val TextPrimary = Color(0xFFE2E8F0)
val TextSecondary = Color(0xFFA0AEC0)
val AccentPrimary = Color(0xFF81E6D9) // accent-primary
val DividerColor = Color(0xFF2D3748) // divider
val SurfaceVariantDark = Color(0xFF1A202C)

@RequiresApi(Build.VERSION_CODES.O)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    mainViewModel: com.example.habits9.ui.MainViewModel = hiltViewModel(),
    onNavigateToCreateHabit: () -> Unit = {},
    onNavigateToHabitDetails: (Long) -> Unit = {},
    onNavigateToSettings: () -> Unit = {},
    onNavigateToManageSections: () -> Unit = {},
    onNavigateToHabitReorder: () -> Unit = {}
) {
    val uiState by mainViewModel.enhancedUiState.collectAsState()

    Scaffold(
        topBar = {
            CustomHeader(
                currentSortType = uiState.currentSortType,
                onAddHabitClick = onNavigateToCreateHabit,
                onSettingsClick = onNavigateToSettings,
                onManageSectionsClick = onNavigateToManageSections,
                onSortTypeSelected = { sortType ->
                    if (sortType == com.example.habits9.data.HabitSortType.CUSTOM_ORDER) {
                        onNavigateToHabitReorder()
                    } else {
                        mainViewModel.updateSortType(sortType)
                    }
                }
            )
        },
        containerColor = BackgroundDark
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (uiState.isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Loading habits...",
                        color = TextPrimary
                    )
                }
            } else {
                VerticalHabitLayout(
                    habitsWithCompletions = uiState.habitsWithCompletions,
                    weekInfo = uiState.weekInfo,
                    onHabitClick = onNavigateToHabitDetails,
                    onCellClick = { habitId, habitType, date ->
                        mainViewModel.onCellClick(habitId, habitType, date)
                    },
                    onPreviousWeek = { mainViewModel.navigateToPreviousWeek() },
                    onNextWeek = { mainViewModel.navigateToNextWeek() }
                )
            }
        }
    }

    // Numerical input dialog for measurable habits
    android.util.Log.d("BugFix", "HomeScreen - Dialog state: isVisible=${uiState.showMeasurableDialog}, habitName=${uiState.measurableDialogHabitName}")
    android.util.Log.d("BugFix", "HomeScreen - Dialog details: habitId=${uiState.measurableDialogHabitId}, unit=${uiState.measurableDialogUnit}")

    NumericalInputDialog(
        isVisible = uiState.showMeasurableDialog,
        habitName = uiState.measurableDialogHabitName,
        unit = uiState.measurableDialogUnit,
        currentValue = uiState.measurableDialogCurrentValue,
        targetValue = uiState.measurableDialogTargetValue,
        targetType = uiState.measurableDialogTargetType,
        onValueChange = { value ->
            android.util.Log.d("BugFix", "Dialog value changed: $value")
            mainViewModel.updateMeasurableDialogValue(value)
        },
        onConfirm = { value ->
            android.util.Log.d("BugFix", "Dialog confirmed with value: $value")
            mainViewModel.saveMeasurableHabitCompletion(
                uiState.measurableDialogHabitId,
                uiState.measurableDialogDate,
                value
            )
        },
        onDismiss = {
            android.util.Log.d("BugFix", "Dialog dismissed")
            mainViewModel.hideMeasurableHabitDialog()
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomHeader(
    currentSortType: com.example.habits9.data.HabitSortType,
    onAddHabitClick: () -> Unit = {},
    onSettingsClick: () -> Unit = {},
    onManageSectionsClick: () -> Unit = {},
    onSortTypeSelected: (com.example.habits9.data.HabitSortType) -> Unit = {}
) {
    var showDropdownMenu by remember { mutableStateOf(false) }
    
    TopAppBar(
        title = {
            Text(
                text = "Habits",
                color = TextPrimary,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = FontFamily.Default
            )
        },
        actions = {
            // Sort menu button
            SortMenuButton(
                currentSortType = currentSortType,
                onSortTypeSelected = onSortTypeSelected
            )

            Spacer(modifier = Modifier.width(8.dp))

            // Add habit button
            IconButton(
                onClick = onAddHabitClick,
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = AccentPrimary,
                        shape = CircleShape
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add Habit",
                    tint = BackgroundDark,
                    modifier = Modifier.size(20.dp)
                )
            }

            Spacer(modifier = Modifier.width(8.dp))
            
            // Settings/More options button with dropdown
            Box {
                IconButton(
                    onClick = { showDropdownMenu = true },
                    modifier = Modifier.size(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = "More Options",
                        tint = TextSecondary,
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                DropdownMenu(
                    expanded = showDropdownMenu,
                    onDismissRequest = { showDropdownMenu = false },
                    modifier = Modifier.background(SurfaceVariantDark)
                ) {
                    DropdownMenuItem(
                        text = {
                            Text(
                                text = "Manage Sections",
                                color = TextPrimary
                            )
                        },
                        onClick = {
                            showDropdownMenu = false
                            onManageSectionsClick()
                        }
                    )
                    DropdownMenuItem(
                        text = {
                            Text(
                                text = "Settings",
                                color = TextPrimary
                            )
                        },
                        onClick = {
                            showDropdownMenu = false
                            onSettingsClick()
                        }
                    )
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = SurfaceVariantDark
        )
    )
}


@Composable
fun VerticalHabitLayout(
    habitsWithCompletions: List<com.example.habits9.ui.HabitWithCompletions>,
    weekInfo: com.example.habits9.ui.WeekInfo,
    onHabitClick: (Long) -> Unit = {},
    onCellClick: (Long, com.example.habits9.data.HabitType, Long) -> Unit = { _, _, _ -> },
    onPreviousWeek: () -> Unit = {},
    onNextWeek: () -> Unit = {}
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Week navigation header
        VerticalDayHeader(
            weekInfo = weekInfo,
            onPreviousWeek = onPreviousWeek,
            onNextWeek = onNextWeek
        )

        // Day of week header (sticky)
        VerticalDayOfWeekHeader(
            weekInfo = weekInfo
        )

        // Main scrollable content - vertical list of habits
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 4.dp), // space-xs from style guide
            verticalArrangement = Arrangement.spacedBy(4.dp) // space-xs between items
        ) {
            // Group habits by section (for now, we'll use a default section)
            // TODO: Implement actual section grouping when sections are available
            item {
                SectionHeader(
                    sectionName = "All Habits",
                    completionPercentage = weekInfo.weeklyCompletionPercentage,
                    modifier = Modifier.padding(vertical = 8.dp) // space-sm
                )
            }

            // Habit rows
            itemsIndexed(
                items = habitsWithCompletions,
                key = { index, habitWithCompletions -> "habit_${habitWithCompletions.habit.uuid}_$index" }
            ) { _, habitWithCompletions ->
                // Create completion states for the last 7 days
                val completionStates = weekInfo.timestamps.takeLast(7).map { timestamp ->
                    val dayLength = 24 * 60 * 60 * 1000L
                    val dayStart = (timestamp / dayLength) * dayLength

                    if (habitWithCompletions.habit.habitType == com.example.habits9.data.HabitType.NUMERICAL) {
                        val valueString = habitWithCompletions.completionValues[dayStart]
                        if (valueString.isNullOrEmpty()) {
                            false
                        } else {
                            val value = valueString.toDoubleOrNull() ?: 0.0
                            when (habitWithCompletions.habit.numericalHabitType) {
                                com.example.habits9.data.NumericalHabitType.AT_LEAST ->
                                    value >= habitWithCompletions.habit.targetValue
                                com.example.habits9.data.NumericalHabitType.AT_MOST ->
                                    value <= habitWithCompletions.habit.targetValue
                            }
                        }
                    } else {
                        habitWithCompletions.completions[dayStart] ?: false
                    }
                }

                HabitRow(
                    habitName = habitWithCompletions.habit.name,
                    completionStates = completionStates,
                    onCompletionToggle = { dayIndex ->
                        val timestamp = weekInfo.timestamps.takeLast(7)[dayIndex]
                        val dayLength = 24 * 60 * 60 * 1000L
                        val dayStart = (timestamp / dayLength) * dayLength
                        onCellClick(
                            habitWithCompletions.habit.id,
                            habitWithCompletions.habit.habitType,
                            dayStart
                        )
                    }
                )
            }
        }
    }
}

@Composable
fun CompletionIndicator(
    completed: Boolean,
    value: String = "",
    isMeasurable: Boolean = false,
    isScheduled: Boolean = true
) {
    // Apply visual styling based on scheduling status
    val alpha = if (isScheduled) 1f else 0.3f
    val disabledColor = DividerColor.copy(alpha = 0.5f)

    if (isMeasurable) {
        // For measurable habits, show only plain text (no background boxes)
        if (value.isNotEmpty() && value != "0") {
            Text(
                text = if (value.length > 3) "${value.take(2)}…" else value,
                color = if (!isScheduled) disabledColor
                       else if (completed) AccentPrimary
                       else TextSecondary,
                fontSize = 8.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier.alpha(alpha)
            )
        } else {
            // Empty measurable habit - show dash or disabled indicator
            Text(
                text = if (isScheduled) "-" else "×",
                color = if (isScheduled) TextSecondary else disabledColor,
                fontSize = 8.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier.alpha(alpha)
            )
        }
    } else if (!isScheduled) {
        // Non-scheduled Yes/No habits - show disabled indicator
        Canvas(
            modifier = Modifier
                .size(18.dp)
                .alpha(alpha)
        ) {
            drawCircle(
                color = disabledColor,
                radius = size.minDimension / 2,
                style = Stroke(width = 1.dp.toPx())
            )
            // Draw an X to indicate disabled
            val strokeWidth = 1.dp.toPx()
            val radius = size.minDimension / 2
            val offset = radius * 0.5f
            drawLine(
                color = disabledColor,
                start = Offset(center.x - offset, center.y - offset),
                end = Offset(center.x + offset, center.y + offset),
                strokeWidth = strokeWidth
            )
            drawLine(
                color = disabledColor,
                start = Offset(center.x - offset, center.y + offset),
                end = Offset(center.x + offset, center.y - offset),
                strokeWidth = strokeWidth
            )
        }
    } else if (completed) {
        // Filled circle for completed Yes/No habits
        Canvas(
            modifier = Modifier.size(18.dp)
        ) {
            drawCircle(
                color = AccentPrimary,
                radius = size.minDimension / 2
            )
        }
    } else {
        // Outlined circle for pending Yes/No habits
        Canvas(
            modifier = Modifier.size(18.dp)
        ) {
            drawCircle(
                color = TextSecondary,
                radius = size.minDimension / 2,
                style = Stroke(width = 1.dp.toPx())
            )
        }
    }
}

@RequiresApi(Build.VERSION_CODES.O)
private fun isWeekStartDay(date: LocalDate, firstDayOfWeek: String = "SUNDAY"): Boolean {
    return if (firstDayOfWeek == "SUNDAY") {
        date.dayOfWeek == DayOfWeek.SUNDAY
    } else {
        date.dayOfWeek == DayOfWeek.MONDAY
    }
}

@Composable
fun VerticalDayHeader(
    weekInfo: com.example.habits9.ui.WeekInfo,
    onPreviousWeek: () -> Unit,
    onNextWeek: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(SurfaceVariantDark)
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left side: "This Week" title and progress
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "This Week: ${(weekInfo.weeklyCompletionPercentage * 100).toInt()}%",
                color = TextPrimary,
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                fontFamily = FontFamily.Default
            )
        }

        // Right side: Week navigation
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            IconButton(
                onClick = onPreviousWeek,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowLeft,
                    contentDescription = "Previous week",
                    tint = TextSecondary,
                    modifier = Modifier.size(20.dp)
                )
            }

            Text(
                text = "W${weekInfo.weekNumber}",
                color = TextSecondary,
                fontSize = 10.sp,
                fontWeight = FontWeight.Normal,
                fontFamily = FontFamily.Monospace
            )

            IconButton(
                onClick = onNextWeek,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "Next week",
                    tint = TextSecondary,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
fun VerticalDayOfWeekHeader(
    weekInfo: com.example.habits9.ui.WeekInfo,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(SurfaceVariantDark)
            .padding(horizontal = 16.dp, vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // Habit name column header (empty space)
        Box(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = "Habit",
                color = TextSecondary,
                fontSize = 10.sp,
                fontWeight = FontWeight.Normal,
                fontFamily = FontFamily.Monospace
            )
        }

        // Day headers - showing last 7 days
        weekInfo.formattedDayAbbreviations.takeLast(7).forEachIndexed { index, dayAbbr ->
            val dayNumber = weekInfo.formattedDates.takeLast(7)[index]

            Column(
                modifier = Modifier.width(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Day abbreviation (MON, TUE, etc.)
                Text(
                    text = dayAbbr.take(2).uppercase(), // Take first 2 letters: MO, TU, WE, TH, FR, SA, SU
                    color = TextSecondary,
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Normal,
                    fontFamily = FontFamily.Monospace,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(2.dp))

                // Day number
                Text(
                    text = dayNumber.split("/").lastOrNull() ?: dayNumber, // Extract day number from date
                    color = TextSecondary,
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Normal,
                    fontFamily = FontFamily.Monospace,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
fun CircularCompletionIndicator(
    isCompleted: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(44.dp) // 44dp touch target as per accessibility requirements
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        if (isCompleted) {
            // Completed state: solid filled circle with accent-primary color
            Box(
                modifier = Modifier
                    .size(18.dp)
                    .background(
                        color = AccentPrimary,
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "Completed",
                    tint = BackgroundDark,
                    modifier = Modifier.size(12.dp)
                )
            }
        } else {
            // Pending state: outlined circle with text-secondary color
            Box(
                modifier = Modifier
                    .size(18.dp)
                    .border(
                        width = 2.dp,
                        color = TextSecondary,
                        shape = CircleShape
                    )
            )
        }
    }
}

@Composable
fun CircularProgressIndicator(
    weeklyCompletionPercentage: Float,
    modifier: Modifier = Modifier
) {
    val percentage = (weeklyCompletionPercentage * 100).toInt()

    Box(
        modifier = modifier.size(32.dp),
        contentAlignment = Alignment.Center
    ) {
        // Background circle
        Canvas(modifier = Modifier.fillMaxSize()) {
            drawCircle(
                color = DividerColor,
                radius = size.minDimension / 2,
                style = Stroke(width = 3.dp.toPx())
            )
        }

        // Progress arc
        Canvas(modifier = Modifier.fillMaxSize()) {
            val sweepAngle = (weeklyCompletionPercentage * 360f)
            drawArc(
                color = AccentPrimary,
                startAngle = -90f, // Start from top
                sweepAngle = sweepAngle,
                useCenter = false,
                style = Stroke(width = 3.dp.toPx(), cap = StrokeCap.Round)
            )
        }

        // Percentage text in center
        Text(
            text = "${percentage}%",
            color = TextPrimary,
            fontSize = 8.sp,
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily.Monospace
        )
    }
}

@Composable
fun SectionHeader(
    sectionName: String,
    completionPercentage: Float,
    isExpanded: Boolean = true,
    onToggleExpanded: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = SurfaceVariantDark,
                shape = RoundedCornerShape(12.dp)
            )
            .clickable { onToggleExpanded() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Section title
        Text(
            text = sectionName,
            color = TextPrimary,
            fontSize = 14.sp,
            fontWeight = FontWeight.SemiBold,
            fontFamily = FontFamily.Default
        )

        // Completion percentage
        Text(
            text = "${(completionPercentage * 100).toInt()}%",
            color = TextSecondary,
            fontSize = 14.sp,
            fontWeight = FontWeight.SemiBold,
            fontFamily = FontFamily.Default
        )
    }
}

@Composable
fun HabitRow(
    habitName: String,
    completionStates: List<Boolean>,
    onCompletionToggle: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Habit name
        Text(
            text = habitName,
            color = TextPrimary,
            fontSize = 12.sp,
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily.Default,
            modifier = Modifier.weight(1f),
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )

        // Completion indicators for each day
        completionStates.forEachIndexed { index, isCompleted ->
            CircularCompletionIndicator(
                isCompleted = isCompleted,
                onClick = { onCompletionToggle(index) },
                modifier = Modifier.padding(horizontal = 2.dp)
            )
        }
    }
}