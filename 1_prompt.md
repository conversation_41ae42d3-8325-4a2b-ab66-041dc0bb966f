# Prompt: Rebuild the Home Screen UI

## A. The Objective & Context

The primary goal of this task is to completely rebuild the user interface of the main habit tracking screen. We are replacing the old design with the new, modern, and compact vertical table layout shown in **`61.jpg`**.

This is a **visual-only refactor**. The developer must not change any of the underlying business logic for habit tracking, data storage, or calculations. The sole focus is to reconstruct the XML layouts and custom views to match the new design.

All specifications for colors, typography, spacing, and component styles are meticulously detailed in the **`style_habits9.md`** file. This guide is the single source of truth for all styling decisions.

## B. Detailed Implementation Plan

### 1. Rebuild the Main Screen with a Vertical Table Layout
- The root layout of the home screen must be refactored to support the new grid-based structure.
- The entire screen content, including all sections and habits, must be vertically scrollable.

### 2. Implement the Vertical Day-of-Week Header
- Create a header row that will be displayed at the top of the habit list.
- This header must contain the three-letter abbreviation for each day of the week (e.g., "MON", "TUE"), oriented vertically.
- Directly below each day's abbreviation, display the corresponding numerical date of the month.
- **Typography**: The text for this header must use the `label-small` style defined in `style_habits9.md`.
- This header should remain fixed ("sticky") at the top of the screen as the user scrolls down through the habit list.

### 3. Implement Collapsible Section Headers
- Habits must be grouped visually into sections (e.g., "Morning").
- Implement a section header view that precisely matches the design in `61.jpg`.
- **Styling**: The header must use the `surface-variant` background color and have a 12px border radius.
- **Content**: It must display the section title on the left and the daily completion percentage for that section on the right.
- **Typography**: The text must use the `display-small` style.

### 4. Rebuild the Habit Row Layout
- Each item in the list should be a habit row containing two main parts:
    1.  The habit name on the far left.
    2.  A row of seven completion indicators, which must align vertically with the day-of-week header above.
- **Typography**: The habit name must use the `body-medium` typography style.

### 5. Implement New Circular Completion Indicators
- The current checkmarks must be replaced with the new circular indicators.
- **Size**: The indicators must be 18px diameter circles.
- **States & Colors**:
    - **Completed:** A solid circle (●) filled with the `accent-primary` color.
    - **Pending:** An outlined circle (○) using the `text-secondary` color for the border.
- **Interaction**: Tapping an indicator should toggle the habit's state for that day. This interaction must feel responsive.
- **Touch Target**: The touch area for each indicator must be a minimum of 44px x 44px.

### 6. Apply the New Design System
- **CRITICAL**: The developer must remove all old styling and strictly implement the new design system from `style_habits9.md`.
- This includes using the exact color tokens for both light and dark themes (`background`, `text-primary`, `accent-primary`, etc.).
- All layout paddings, margins, and gaps between elements must use the specified spacing tokens (`space-xs`, `space-md`, etc.). For example, the spacing between cells in the grid should be `space-xs` (4px).

## C. Meticulous Verification Plan

1.  **Full Layout Verification**:
    - **CRITICAL**: Open the app and confirm the home screen is a pixel-perfect visual match of the layout in `61.jpg`.
    - Verify the layout is responsive and does not break on different screen sizes.
    - Confirm the entire list scrolls vertically and smoothly.

2.  **Header and Section Verification**:
    - Check that the vertical day-of-week header is present, displays the correct days and dates, and uses the correct `label-small` font style.
    - Verify the section headers display the correct title and percentage and use the `display-small` font style.
    - Confirm the background color and border radius of the section headers are correct per `style_habits9.md`.

3.  **Habit Row Verification**:
    - **CRITICAL**: Verify that completion indicators are 18px circles and that their colors for "completed" (`accent-primary`) and "pending" (`text-secondary`) states are correct.
    - Tap several indicators for different habits and dates to confirm that the state toggles correctly and the underlying data is updated.
    - Check that habit names use the `body-medium` font style.

4.  **Theme Functionality Verification**:
    - **CRITICAL**: Switch the device between **Light and Dark modes**. Verify that every UI element (backgrounds, text, icons, dividers) correctly adapts to the theme using the colors defined in `style_habits9.md`.

5.  **Spacing and Alignment Verification**:
    - Meticulously inspect the padding, margins, and alignment of all elements. Ensure the grid is perfectly aligned and all spacing matches the tokens in `style_habits9.md`.

## D. Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.